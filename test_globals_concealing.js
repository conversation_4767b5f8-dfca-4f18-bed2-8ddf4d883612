const fs = require('fs');
const luamin = require('./luamin.js');

// Test Lua code with global variables
const testCode = `
-- Test global variables
local myVar = 10
myGlobalFunc = function(x)
    return x * 2
end

myGlobalVar = 42

function anotherGlobalFunc()
    print("Hello from global function")
    return myGlobalVar + myVar
end

-- Call the functions
print(myGlobalFunc(5))
print(anotherGlobalFunc())
`;

console.log('Original Lua code:');
console.log(testCode);
console.log('\n' + '='.repeat(80) + '\n');

try {
    const result = luamin.minify(testCode);
    
    console.log('Obfuscated code with globals concealing:');
    console.log(result);
    console.log('\n' + '='.repeat(80) + '\n');
    
    // Save the obfuscated code to a file for testing
    fs.writeFileSync('test_output_globals.lua', result);
    console.log('Obfuscated code saved to test_output_globals.lua');
    
    // Check if globals were concealed
    const hasGlobalsArray = result.includes('globals') || result.includes('decodeGlobal');
    const hasEncodedGlobals = result.includes('myGlobalFunc') === false && result.includes('myGlobalVar') === false;
    
    console.log('\nGlobals concealing analysis:');
    console.log('- Has globals array/decode function:', hasGlobalsArray);
    console.log('- Original global names hidden:', hasEncodedGlobals);
    
    if (hasGlobalsArray && hasEncodedGlobals) {
        console.log('✅ Globals concealing appears to be working!');
    } else {
        console.log('❌ Globals concealing may not be working as expected.');
    }
    
} catch (error) {
    console.error('Error during obfuscation:', error);
}
