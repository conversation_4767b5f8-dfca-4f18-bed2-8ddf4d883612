const fs = require('fs');

// Create a modified version of luamin for debugging
const luaminCode = fs.readFileSync('./luamin.js', 'utf8');

// Add debug logging to the collectUserDefinedGlobals function
const debugLuaminCode = luaminCode.replace(
    'collectUserDefinedGlobals = function(ast) {',
    `collectUserDefinedGlobals = function(ast) {
        console.log('DEBUG: Collecting user-defined globals...');`
).replace(
    'userDefinedGlobals.push(variable.name);',
    `userDefinedGlobals.push(variable.name);
                            console.log('DEBUG: Found global variable:', variable.name);`
).replace(
    'userDefinedGlobals.push(node.identifier.name);',
    `userDefinedGlobals.push(node.identifier.name);
                    console.log('DEBUG: Found global function:', node.identifier.name);`
).replace(
    'traverse(ast);',
    `traverse(ast);
        console.log('DEBUG: Final userDefinedGlobals:', userDefinedGlobals);`
).replace(
    'if (shouldConcealGlobal(expression) && !options.preserveIdentifiers) {',
    `if (shouldConcealGlobal(expression) && !options.preserveIdentifiers) {
                console.log('DEBUG: Concealing global:', expression.name);`
);

// Write the debug version
fs.writeFileSync('./luamin_debug.js', debugLuaminCode);

// Test with debug version
const luaminDebug = require('./luamin_debug.js');

const testCode = `
myGlobalFunc = function(x)
    return x * 2
end

myGlobalVar = 42

function anotherGlobalFunc()
    print("Hello")
    return myGlobalVar
end
`;

console.log('Testing with debug version...\n');

try {
    const result = luaminDebug.minify(testCode);
    console.log('\nResult length:', result.length);
    console.log('Contains myGlobalFunc:', result.includes('myGlobalFunc'));
    console.log('Contains myGlobalVar:', result.includes('myGlobalVar'));
    console.log('Contains anotherGlobalFunc:', result.includes('anotherGlobalFunc'));
} catch (error) {
    console.error('Error:', error);
}
