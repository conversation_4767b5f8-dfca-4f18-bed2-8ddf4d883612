const luamin = require('./luamin.js');

// Simple test to see if globals concealing is working
const testCode = `myGlobalVar = 42`;

console.log('Testing simple global assignment...\n');
console.log('Original:', testCode);

try {
    const result = luamin.minify(testCode);
    console.log('Obfuscated:', result);
    console.log('Contains myGlobalVar:', result.includes('myGlobalVar'));
    
    // Check if there's a globals array
    const hasGlobalsArray = result.match(/local\s+\w+\s*=\s*\{[^}]*\}/g);
    console.log('Found arrays:', hasGlobalsArray ? hasGlobalsArray.length : 0);
    
} catch (error) {
    console.error('Error:', error);
}
