const fs = require('fs');

// Create a debug version to check traversal
const luaminCode = fs.readFileSync('./luamin.js', 'utf8');

const debugCode = luaminCode.replace(
    `var traverse = function(node) {
			if (!node || typeof node !== 'object') {
				return;
			}
			
			// Handle assignment statements (e.g., myGlobalVar = 42)
			if (node.type === 'AssignmentStatement') {
				each(node.variables, function(variable) {
					if (variable.type === 'Identifier' && variable.isLocal !== true) {
						if (indexOf(userDefinedGlobals, variable.name) === -1) {
							userDefinedGlobals.push(variable.name);
						}
					}
				});
			}`,
    `var traverse = function(node) {
			if (!node || typeof node !== 'object') {
				return;
			}
			
			console.log('DEBUG: Traversing node type:', node.type);
			
			// Handle assignment statements (e.g., myGlobalVar = 42)
			if (node.type === 'AssignmentStatement') {
				console.log('DEBUG: Found AssignmentStatement');
				each(node.variables, function(variable) {
					console.log('DEBUG: Processing variable:', variable.name, 'isLocal:', variable.isLocal);
					if (variable.type === 'Identifier' && variable.isLocal !== true) {
						console.log('DEBUG: Adding to userDefinedGlobals:', variable.name);
						if (indexOf(userDefinedGlobals, variable.name) === -1) {
							userDefinedGlobals.push(variable.name);
						}
					}
				});
			}`
);

fs.writeFileSync('./luamin_debug6.js', debugCode);

// Test with the debug version
const luaminDebug = require('./luamin_debug6.js');

const testCode = `myGlobalVar = 42`;

console.log('Testing traversal debug...\n');

try {
    const result = luaminDebug.minify(testCode);
    console.log('\nDone.');
} catch (error) {
    console.error('Error:', error);
}
