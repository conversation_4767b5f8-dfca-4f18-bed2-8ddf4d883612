const luaparse = require('luaparse');

const testCode = `
myGlobalFunc = function(x)
    return x * 2
end

myGlobalVar = 42

function anotherGlobalFunc()
    print("Hello")
    return myGlobalVar
end
`;

console.log('Parsing and analyzing AST structure...\n');

try {
    const ast = luaparse.parse(testCode);
    
    console.log('AST body length:', ast.body.length);
    
    ast.body.forEach((statement, index) => {
        console.log(`\nStatement ${index}:`);
        console.log('Type:', statement.type);
        
        if (statement.type === 'AssignmentStatement') {
            console.log('Variables:');
            statement.variables.forEach((variable, varIndex) => {
                console.log(`  Variable ${varIndex}:`, {
                    type: variable.type,
                    name: variable.name,
                    isLocal: variable.isLocal
                });
            });
        }
        
        if (statement.type === 'FunctionDeclaration') {
            console.log('Function details:', {
                isLocal: statement.isLocal,
                identifier: statement.identifier ? statement.identifier.name : null
            });
        }
    });
    
    // Manual collection of globals
    const manualGlobals = [];
    
    ast.body.forEach((statement) => {
        if (statement.type === 'AssignmentStatement') {
            statement.variables.forEach((variable) => {
                if (variable.type === 'Identifier' && !variable.isLocal) {
                    if (manualGlobals.indexOf(variable.name) === -1) {
                        manualGlobals.push(variable.name);
                    }
                }
            });
        }
        
        if (statement.type === 'FunctionDeclaration' && !statement.isLocal && statement.identifier) {
            if (manualGlobals.indexOf(statement.identifier.name) === -1) {
                manualGlobals.push(statement.identifier.name);
            }
        }
    });
    
    console.log('\nManually collected globals:', manualGlobals);
    
} catch (error) {
    console.error('Error:', error);
}
