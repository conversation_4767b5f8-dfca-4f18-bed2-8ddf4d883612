const luaparse = require('luaparse');

// Test Lua code with global variables
const testCode = `
myGlobalFunc = function(x)
    return x * 2
end

myGlobalVar = 42

function anotherGlobalFunc()
    print("Hello")
    return myGlobalVar
end
`;

console.log('Parsing Lua code to examine AST structure...\n');

try {
    const ast = luaparse.parse(testCode);
    
    console.log('AST globals:', ast.globals);
    console.log('\nFull AST structure:');
    console.log(JSON.stringify(ast, null, 2));
    
} catch (error) {
    console.error('Error parsing Lua code:', error);
}
