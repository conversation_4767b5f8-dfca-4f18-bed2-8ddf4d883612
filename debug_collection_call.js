const fs = require('fs');

// Create a debug version to check if collection is called
const luaminCode = fs.readFileSync('./luamin.js', 'utf8');

const debugCode = luaminCode.replace(
    `// Collect user-defined global variables from AST
	var collectUserDefinedGlobals = function(ast) {
		userDefinedGlobals = []; // Reset the array`,
    `// Collect user-defined global variables from AST
	var collectUserDefinedGlobals = function(ast) {
		console.log('DEBUG: collectUserDefinedGlobals called');
		userDefinedGlobals = []; // Reset the array`
).replace(
    `// Start traversal from the AST body
		if (ast && ast.body) {
			each(ast.body, traverse);
		}`,
    `// Start traversal from the AST body
		if (ast && ast.body) {
			console.log('DEBUG: Starting traversal of', ast.body.length, 'statements');
			each(ast.body, traverse);
		}
		console.log('DEBUG: Collection finished, userDefinedGlobals:', userDefinedGlobals);`
);

fs.writeFileSync('./luamin_debug5.js', debugCode);

// Test with the debug version
const luaminDebug = require('./luamin_debug5.js');

const testCode = `myGlobalVar = 42`;

console.log('Testing collection call debug...\n');

try {
    const result = luaminDebug.minify(testCode);
    console.log('\nDone.');
} catch (error) {
    console.error('Error:', error);
}
