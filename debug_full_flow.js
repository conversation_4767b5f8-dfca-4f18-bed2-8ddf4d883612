const fs = require('fs');

// Create a debug version with full flow logging
const luaminCode = fs.readFileSync('./luamin.js', 'utf8');

const debugCode = luaminCode.replace(
    'collectUserDefinedGlobals(ast);',
    `collectUserDefinedGlobals(ast);
		console.log('DEBUG: After collection, userDefinedGlobals:', userDefinedGlobals);`
).replace(
    `var shouldConcealGlobal = function(expression) {
		// Only conceal global identifiers that are user-defined (not built-in functions)
		if (!shouldConcealGlobals || expression.isLocal) {
			return false;
		}
		
		// List of built-in Lua functions/variables that should not be concealed
		var builtInGlobals = [
			'print', 'type', 'pairs', 'ipairs', 'next', 'tonumber', 'tostring',
			'string', 'table', 'math', 'io', 'os', 'debug', 'coroutine',
			'load', 'loadstring', 'loadfile', 'dofile', 'require',
			'getmetatable', 'setmetatable', 'rawget', 'rawset', 'rawlen',
			'select', 'unpack', 'pcall', 'xpcall', 'error', 'assert',
			'_G', '_VERSION', 'arg'
		];
		
		// Don't conceal built-in globals
		if (indexOf(builtInGlobals, expression.name) > -1) {
			return false;
		}
		
		// Conceal user-defined globals
		return indexOf(userDefinedGlobals, expression.name) > -1;
	};`,
    `var shouldConcealGlobal = function(expression) {
		console.log('DEBUG: shouldConcealGlobal called for:', expression.name);
		console.log('DEBUG: shouldConcealGlobals:', shouldConcealGlobals);
		console.log('DEBUG: expression.isLocal:', expression.isLocal);
		console.log('DEBUG: userDefinedGlobals:', userDefinedGlobals);
		
		// Only conceal global identifiers that are user-defined (not built-in functions)
		if (!shouldConcealGlobals || expression.isLocal) {
			console.log('DEBUG: Returning false - shouldConcealGlobals or isLocal check');
			return false;
		}
		
		// List of built-in Lua functions/variables that should not be concealed
		var builtInGlobals = [
			'print', 'type', 'pairs', 'ipairs', 'next', 'tonumber', 'tostring',
			'string', 'table', 'math', 'io', 'os', 'debug', 'coroutine',
			'load', 'loadstring', 'loadfile', 'dofile', 'require',
			'getmetatable', 'setmetatable', 'rawget', 'rawset', 'rawlen',
			'select', 'unpack', 'pcall', 'xpcall', 'error', 'assert',
			'_G', '_VERSION', 'arg'
		];
		
		// Don't conceal built-in globals
		if (indexOf(builtInGlobals, expression.name) > -1) {
			console.log('DEBUG: Returning false - built-in global');
			return false;
		}
		
		// Conceal user-defined globals
		var result = indexOf(userDefinedGlobals, expression.name) > -1;
		console.log('DEBUG: Final result for', expression.name, ':', result);
		return result;
	};`
);

fs.writeFileSync('./luamin_debug4.js', debugCode);

// Test with the debug version
const luaminDebug = require('./luamin_debug4.js');

const testCode = `myGlobalVar = 42`;

console.log('Testing with full flow debug...\n');

try {
    const result = luaminDebug.minify(testCode);
    console.log('\nResult contains myGlobalVar:', result.includes('myGlobalVar'));
} catch (error) {
    console.error('Error:', error);
}
