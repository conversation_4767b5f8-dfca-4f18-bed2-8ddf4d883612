const fs = require('fs');

// Create a debug version with collection logging
const luaminCode = fs.readFileSync('./luamin.js', 'utf8');

const debugCode = luaminCode.replace(
    `// Handle assignment statements (e.g., myGlobalVar = 42)
			if (node.type === 'AssignmentStatement') {
				each(node.variables, function(variable) {
					if (variable.type === 'Identifier' && variable.isLocal !== true) {
						if (indexOf(userDefinedGlobals, variable.name) === -1) {
							userDefinedGlobals.push(variable.name);
						}
					}
				});
			}`,
    `// Handle assignment statements (e.g., myGlobalVar = 42)
			if (node.type === 'AssignmentStatement') {
				console.log('DEBUG: Found AssignmentStatement with', node.variables.length, 'variables');
				each(node.variables, function(variable) {
					console.log('DEBUG: Variable:', variable.name, 'type:', variable.type, 'isLocal:', variable.isLocal);
					if (variable.type === 'Identifier' && variable.isLocal !== true) {
						console.log('DEBUG: Adding global variable:', variable.name);
						if (indexOf(userDefinedGlobals, variable.name) === -1) {
							userDefinedGlobals.push(variable.name);
						}
					}
				});
			}`
);

fs.writeFileSync('./luamin_debug3.js', debugCode);

// Test with the debug version
const luaminDebug = require('./luamin_debug3.js');

const testCode = `myGlobalVar = 42`;

console.log('Testing with collection debug...\n');

try {
    const result = luaminDebug.minify(testCode);
    console.log('\nDone.');
} catch (error) {
    console.error('Error:', error);
}
