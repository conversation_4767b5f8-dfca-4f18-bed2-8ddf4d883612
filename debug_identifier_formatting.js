const fs = require('fs');

// Create a debug version with identifier formatting logging
const luaminCode = fs.readFileSync('./luamin.js', 'utf8');

const debugCode = luaminCode.replace(
    `if (expressionType == 'Identifier') {

			// Check if we should conceal this global identifier
			if (shouldConcealGlobal(expression) && !options.preserveIdentifiers) {
				// Use the original name for encoding (before renaming)
				result = encodeGlobalName(expression.name);
			} else {
				// Always rename identifiers unless explicitly preserving them
				result = shouldRenameIdentifier(expression) && !options.preserveIdentifiers
					? generateIdentifier(expression.name)
					: expression.name;
			}`,
    `if (expressionType == 'Identifier') {
			console.log('DEBUG: Processing identifier:', expression.name, 'isLocal:', expression.isLocal, 'preserveIdentifiers:', options.preserveIdentifiers);
			
			// Check if we should conceal this global identifier
			var shouldConceal = shouldConcealGlobal(expression);
			console.log('DEBUG: Should conceal', expression.name, ':', shouldConceal);
			
			if (shouldConceal && !options.preserveIdentifiers) {
				console.log('DEBUG: Concealing global:', expression.name);
				// Use the original name for encoding (before renaming)
				result = encodeGlobalName(expression.name);
			} else {
				// Always rename identifiers unless explicitly preserving them
				result = shouldRenameIdentifier(expression) && !options.preserveIdentifiers
					? generateIdentifier(expression.name)
					: expression.name;
			}`
).replace(
    'collectUserDefinedGlobals(ast);',
    `collectUserDefinedGlobals(ast);
		console.log('DEBUG: Collected globals:', userDefinedGlobals);`
);

fs.writeFileSync('./luamin_debug2.js', debugCode);

// Test with the debug version
const luaminDebug = require('./luamin_debug2.js');

const testCode = `myGlobalVar = 42`;

console.log('Testing with detailed identifier debug...\n');

try {
    const result = luaminDebug.minify(testCode);
    console.log('\nResult contains myGlobalVar:', result.includes('myGlobalVar'));
} catch (error) {
    console.error('Error:', error);
}
